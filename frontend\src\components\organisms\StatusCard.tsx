import React from 'react';
import { View, StyleSheet } from 'react-native';
import { colors, spacing, borderRadius, shadows } from '../../constants';
import { Text, Button, Icon } from '../atoms';
import { UserInfo } from '../molecules';

interface StatusCardProps {
  user: {
    name: string;
    avatarUrl?: string;
  };
  location: string;
  timeRange: string;
  timer: string; // Format: "HH:MM:SS"
  onClockOut: () => void;
  isInProgress?: boolean;
}

const StatusCard: React.FC<StatusCardProps> = ({
  user,
  location,
  timeRange,
  timer,
  onClockOut,
  isInProgress = true
}) => {
  return (
    <View style={styles.container}>
      {/* Timer Display */}
      <View style={styles.timerSection}>
        <Text variant="h1" color="textOnPrimary" style={styles.timerText}>
          {timer}
        </Text>
      </View>

      {/* User Info */}
      <View style={styles.userSection}>
        <UserInfo
          name={user.name}
          avatarSource={user.avatarUrl ? { uri: user.avatarUrl } : undefined}
          size="medium"
          textColor="textOnPrimary"
          secondaryTextColor="textOnPrimary"
        />
      </View>

      {/* Location */}
      <View style={styles.locationSection}>
        <View style={styles.row}>
          <Icon name="location-outline" size={16} color="textOnPrimary" />
          <Text variant="bodySmall" color="textOnPrimary" style={styles.locationText}>
            {location}
          </Text>
        </View>
      </View>

      {/* Time Range */}
      <View style={styles.timeSection}>
        <View style={styles.row}>
          <Icon name="time-outline" size={16} color="textOnPrimary" />
          <Text variant="bodySmall" color="textOnPrimary" style={styles.timeText}>
            {timeRange}
          </Text>
        </View>
      </View>

      {/* Clock Out Button */}
      <Button
        variant="secondary"
        onPress={onClockOut}
        fullWidth
        style={styles.button}
      >
        <View style={styles.buttonContent}>
          <Icon name="time-outline" size={20} color="primary" />
          <Text variant="button" color="primary" style={styles.buttonText}>
            {isInProgress ? 'Clock-Out' : 'Clock-In'}
          </Text>
        </View>
      </Button>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.primary,
    borderRadius: borderRadius.lg,
    padding: spacing.lg,
    ...shadows.card,
  },
  userSection: {
    // Ensure proper spacing for user info
  },
  scheduleSection: {
    // Ensure proper spacing for schedule details
  },
  divider: {
    height: 1,
    backgroundColor: colors.primaryLight,
    marginVertical: spacing.md,
    opacity: 0.3,
  },
  button: {
    marginTop: spacing.md,
    backgroundColor: colors.white,
  },
});

export default StatusCard;
