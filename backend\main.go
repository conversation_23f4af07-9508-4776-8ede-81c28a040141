package main

import (
	"os"
	"os/exec"
	"path/filepath"
)

func main() {
	// Get the directory where this main.go file is located
	dir, err := filepath.Abs(filepath.Dir(os.Args[0]))
	if err != nil {
		panic(err)
	}

	// Change to the backend directory
	if err := os.Chdir(dir); err != nil {
		panic(err)
	}

	// Run the actual server from cmd/server
	cmd := exec.Command("go", "run", "./cmd/server")
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr
	cmd.Stdin = os.Stdin

	if err := cmd.Run(); err != nil {
		panic(err)
	}
}
