import React from 'react';
import { View, StyleSheet } from 'react-native';
import { colors, spacing } from '../../constants';
import { Text } from '../atoms';
import { SummaryCard } from '../molecules';
import { ScheduleStats } from '../../services/types';

interface StatsOverviewProps {
  stats: ScheduleStats;
}

const StatsOverview: React.FC<StatsOverviewProps> = ({ stats }) => {
  return (
    <View style={styles.container}>
      {/* Missed Schedules - Full Width */}
      <SummaryCard
        value={stats.missed}
        label="Missed Scheduled"
        valueColor="error"
      />

      {/* Two Column Layout */}
      <View style={styles.row}>
        <SummaryCard
          value={stats.upcoming}
          label="Upcoming Today's Schedule"
          valueColor="accent"
        />

        <SummaryCard
          value={stats.completed}
          label="Today's Completed Schedule"
          valueColor="success"
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    gap: spacing.lg,
  },
  row: {
    flexDirection: 'row',
    gap: spacing.lg,
  },
});

export default StatsOverview;
