import React from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  RefreshControl,
  Alert
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { colors, spacing } from '../constants';
import { Text } from '../components/atoms';
import {
  StatusCard,
  StatsOverview,
  ScheduleList
} from '../components/organisms';
import { Schedule } from '../services/types';
import { useTodaySchedules, useScheduleStats, useStartVisit, useEndVisit } from '../hooks/useSchedules';

const HomeScreen: React.FC = () => {
  // React Query hooks
  const {
    data: schedules = [],
    isLoading,
    refetch: refetchSchedules
  } = useTodaySchedules();

  const {
    data: stats,
    refetch: refetchStats
  } = useScheduleStats();

  const startVisitMutation = useStartVisit();
  const endVisitMutation = useEndVisit();

  // Mock user data - in real app this would come from auth context
  const currentUser = {
    name: '<PERSON>',
    avatarUrl: undefined, // Will show initials
  };

  // Mock current schedule for status card
  const currentSchedule = schedules.find(s => s.status === 'in_progress') || schedules[0];

  const onRefresh = async () => {
    await Promise.all([
      refetchSchedules(),
      refetchStats(),
    ]);
  };

  const handleClockIn = (schedule: Schedule) => {
    Alert.alert(
      'Clock In',
      `Clock in for ${schedule.client_name}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clock In',
          onPress: () => {
            // Mock location data - in real app get from device GPS
            const mockLocation = {
              start_latitude: 40.7128,
              start_longitude: -74.0060,
            };

            startVisitMutation.mutate({
              scheduleId: schedule.id,
              data: mockLocation,
            });
          }
        },
      ]
    );
  };

  const handleClockOut = (schedule?: Schedule) => {
    if (!schedule) return;

    const scheduleName = schedule.client_name || 'current visit';
    Alert.alert(
      'Clock Out',
      `Clock out from ${scheduleName}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clock Out',
          onPress: () => {
            // Mock location data - in real app get from device GPS
            const mockLocation = {
              end_latitude: 40.7128,
              end_longitude: -74.0060,
              notes: 'Visit completed successfully',
            };

            endVisitMutation.mutate({
              scheduleId: schedule.id,
              data: mockLocation,
            });
          }
        },
      ]
    );
  };

  const handleViewProgress = (schedule: Schedule) => {
    // TODO: Navigate to progress/report screen
    console.log('View progress for schedule:', schedule.id);
  };

  const handleMoreOptions = (schedule: Schedule) => {
    // TODO: Show action sheet with more options
    console.log('More options for schedule:', schedule.id);
  };

  const handleSeeAll = () => {
    // TODO: Navigate to all schedules screen
    console.log('See all schedules');
  };

  if (isLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text variant="body" color="textSecondary">
            Loading...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.content}
        refreshControl={
          <RefreshControl
            refreshing={startVisitMutation.isPending || endVisitMutation.isPending}
            onRefresh={onRefresh}
          />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Welcome Header */}
        <Text variant="h2" color="textPrimary" style={styles.welcome}>
          Welcome {currentUser.name}!
        </Text>

        {/* Current Status Card */}
        {currentSchedule && (
          <StatusCard
            user={currentUser}
            location={currentSchedule.location.address}
            dateTime={new Date(currentSchedule.start_time).toLocaleDateString('en-US', {
              weekday: 'short',
              day: '2-digit',
              month: 'short',
              year: 'numeric'
            })}
            timeRange={`${new Date(currentSchedule.start_time).toLocaleTimeString('en-US', {
              hour: '2-digit',
              minute: '2-digit',
              hour12: false
            })} - ${new Date(currentSchedule.end_time).toLocaleTimeString('en-US', {
              hour: '2-digit',
              minute: '2-digit',
              hour12: false
            })}`}
            onClockOut={() => handleClockOut(currentSchedule)}
            isInProgress={currentSchedule.status === 'in_progress'}
          />
        )}

        {/* Statistics Overview */}
        {stats && <StatsOverview stats={stats} />}

        {/* Schedule List */}
        <ScheduleList
          schedules={schedules}
          onClockIn={handleClockIn}
          onClockOut={handleClockOut}
          onViewProgress={handleViewProgress}
          onMoreOptions={handleMoreOptions}
          onSeeAll={handleSeeAll}
        />
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: spacing.screenPadding,
    gap: spacing.xl,
    paddingBottom: spacing.xxxl,
  },
  welcome: {
    marginBottom: spacing.lg,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default HomeScreen;
